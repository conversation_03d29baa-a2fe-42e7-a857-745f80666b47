# Function Call 功能说明

## 概述

Function Call 是大模型的通用功能，允许AI在对话过程中调用外部工具来获取实时信息或执行特定操作。本项目实现了完整的Function Call框架，包括工具注册、管理和执行。

## 核心组件

### 1. FunctionCallManager 接口
负责工具的注册、管理和调用的核心接口。

**主要方法：**
- `registerTool(Tool tool)` - 注册工具
- `callTool(String toolName, String/Map arguments)` - 调用工具
- `getRegisteredTools()` - 获取已注册工具列表
- `getOpenAIFunctionDefinitions()` - 获取OpenAI格式的函数定义

### 2. Tool 类
定义工具的元数据和执行逻辑。

**组成部分：**
- 工具名称和描述
- 参数定义（类型、是否必需、默认值等）
- 执行函数（ToolFunction接口）

### 3. ToolExecutionResult 类
封装工具执行结果，包括成功/失败状态、结果内容、执行时间等。

## 已实现的工具

### 1. DirectoryListTool (目录列表工具)
类似于 `ls` 命令，可以列出指定目录的文件和子目录。

**参数：**
- `path` (string) - 目录路径，默认为当前目录
- `show_hidden` (boolean) - 是否显示隐藏文件
- `detailed` (boolean) - 是否显示详细信息
- `sort_by` (string) - 排序方式：name/size/modified/type

**示例调用：**
```java
Map<String, Object> args = new HashMap<>();
args.put("path", "src");
args.put("detailed", true);
args.put("sort_by", "size");

ToolExecutionResult result = manager.callTool("list_directory", args);
```

### 2. SystemInfoTool (系统信息工具)
获取系统信息，包括操作系统、内存、运行时等。

**参数：**
- `info_type` (string) - 信息类型：all/os/memory/runtime/properties

**示例调用：**
```java
Map<String, Object> args = new HashMap<>();
args.put("info_type", "memory");

ToolExecutionResult result = manager.callTool("get_system_info", args);
```

## 使用方法

### 1. 基本使用

```java
// 创建管理器
FunctionCallManager manager = new DefaultFunctionCallManager();

// 注册工具
manager.registerTool(DirectoryListTool.createTool());
manager.registerTool(SystemInfoTool.createTool());

// 调用工具
Map<String, Object> args = new HashMap<>();
args.put("path", ".");
ToolExecutionResult result = manager.callTool("list_directory", args);

System.out.println(result.getFormattedResult());
```

### 2. 与ChatInterface集成

```java
// 创建组件
FunctionCallManager functionManager = new DefaultFunctionCallManager();
functionManager.registerTool(DirectoryListTool.createTool());
functionManager.registerTool(SystemInfoTool.createTool());

OpenaiClient openaiClient = new OpenaiClient();
openaiClient.setFunctionCallManager(functionManager);

CopilotChatAssistant chatAssistant = new CopilotChatAssistant();

// 处理用户消息
chatAssistant.onMessageSent(userMessage -> {
    List<Message> response = openaiClient.generateOpenaiResponse(messageHistory, userMessage);
    chatAssistant.setMessages(response);
});
```

### 3. 自定义工具

```java
public class CustomTool {
    public static Tool createTool() {
        List<Tool.ToolParameter> parameters = Arrays.asList(
            new Tool.ToolParameter("param1", "string", "参数描述", true),
            new Tool.ToolParameter("param2", "number", "数字参数", false, 0)
        );
        
        return new Tool("custom_tool", "自定义工具描述", parameters, CustomTool::execute);
    }
    
    private static ToolExecutionResult execute(Map<String, Object> arguments) throws Exception {
        // 实现工具逻辑
        String param1 = (String) arguments.get("param1");
        Number param2 = (Number) arguments.get("param2");
        
        // 执行操作
        String result = "处理结果: " + param1 + ", " + param2;
        
        return new ToolExecutionResult(result);
    }
}
```

## 工作流程

1. **用户发送消息** - 用户在ChatInterface中输入自然语言请求
2. **AI分析意图** - OpenAI API分析用户意图，决定是否需要调用工具
3. **工具调用请求** - 如果需要，API返回tool_calls请求
4. **执行工具** - FunctionCallManager执行相应工具
5. **返回结果** - 工具结果作为tool消息发送回AI
6. **生成回复** - AI基于工具结果生成最终用户回复

## 测试和演示

运行以下测试类来体验Function Call功能：

1. `SimpleFunctionCallDemo` - 基本功能演示
2. `CompleteFunctionCallDemo` - 完整功能演示
3. `FunctionCallManagerTest` - 管理器功能测试
4. `FunctionCallIntegrationTest` - 集成测试

## 扩展性

框架设计具有良好的扩展性：

1. **添加新工具** - 实现Tool类并注册到管理器
2. **自定义参数验证** - 在工具执行前进行参数校验
3. **结果格式化** - 自定义ToolExecutionResult的格式化方法
4. **错误处理** - 通过ToolExecutionException处理各种错误情况

## 注意事项

1. **安全性** - 工具执行可能涉及系统操作，需要适当的权限控制
2. **性能** - 工具执行可能耗时，建议在后台线程中执行
3. **错误处理** - 妥善处理工具执行异常，避免影响用户体验
4. **参数验证** - 确保工具参数的有效性和安全性

## 示例用户请求

以下是一些可以触发Function Call的用户请求示例：

- "请列出当前目录的文件"
- "显示src文件夹的详细信息"
- "查看系统内存使用情况"
- "获取操作系统信息"
- "temp_tests目录里有什么文件？"
- "显示系统运行时信息"
