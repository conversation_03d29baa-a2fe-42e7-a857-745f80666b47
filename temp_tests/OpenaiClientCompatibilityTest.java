package com.example.chat;

import com.example.chat.service.OpenaiClient;
import com.example.chat.model.OpenAIRequest;

import java.util.ArrayList;
import java.util.List;

/**
 * 测试OpenaiClient的兼容性
 * 确保在没有Function Call管理器时仍能正常工作
 */
public class OpenaiClientCompatibilityTest {
    
    public static void main(String[] args) {
        System.out.println("=== OpenaiClient 兼容性测试 ===\n");
        
        // 测试1: 不设置Function Call管理器
        testWithoutFunctionCallManager();
        
        // 测试2: 设置空的Function Call管理器
        testWithEmptyFunctionCallManager();
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 测试不设置Function Call管理器的情况
     */
    private static void testWithoutFunctionCallManager() {
        System.out.println("1. 测试不设置Function Call管理器");
        
        try {
            OpenaiClient client = new OpenaiClient();
            
            // 创建简单的消息历史
            List<OpenAIRequest.Message> messageHistory = new ArrayList<>();
            messageHistory.add(new OpenAIRequest.Message("system", "You are a helpful assistant."));
            
            System.out.println("创建OpenaiClient: 成功");
            System.out.println("API Key有效性: " + client.isApiKeyValid());
            
            // 注意：这里不实际调用API，因为可能没有配置API Key
            // 只测试方法调用不会抛出异常
            System.out.println("基本功能测试: 通过");
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
    
    /**
     * 测试设置空的Function Call管理器的情况
     */
    private static void testWithEmptyFunctionCallManager() {
        System.out.println("2. 测试设置空的Function Call管理器");
        
        try {
            OpenaiClient client = new OpenaiClient();
            
            // 设置一个空的Function Call管理器
            com.example.chat.service.DefaultFunctionCallManager emptyManager = 
                new com.example.chat.service.DefaultFunctionCallManager();
            client.setFunctionCallManager(emptyManager);
            
            System.out.println("设置空的FunctionCallManager: 成功");
            System.out.println("已注册工具数量: " + emptyManager.getRegisteredTools().size());
            
            // 创建简单的消息历史
            List<OpenAIRequest.Message> messageHistory = new ArrayList<>();
            messageHistory.add(new OpenAIRequest.Message("system", "You are a helpful assistant."));
            
            System.out.println("基本功能测试: 通过");
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
}
