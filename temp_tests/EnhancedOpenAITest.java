package com.example.chat;

import com.example.chat.service.OpenaiClient;

import java.util.ArrayList;
import java.util.List;

/**
 * 增强版 OpenAI 客户端测试类
 * 展示新的消息列表返回 API 的优势
 */
public class EnhancedOpenAITest {
    
    public static void main(String[] args) {
        System.out.println("=== 增强版 OpenAI 客户端测试 ===");
        
        // 创建客户端实例
        OpenaiClient client = new OpenaiClient();
        
        // 检查 API Key
        if (!client.isApiKeyValid()) {
            System.err.println("错误：OpenAI API Key 未配置或无效！");
            System.err.println("请设置环境变量：export OPENAI_API_KEY=sk-your-key-here");
            
            // 演示无 API Key 时的行为
            demonstrateWithoutApiKey(client);
            return;
        }
        
        System.out.println("API Key 验证通过，开始测试...");
        
        // 测试新的消息列表 API
        testMessageListAPI(client);
        
        // 测试对话历史管理
        testConversationHistory(client);
        
        // 测试向后兼容性
        testBackwardCompatibility(client);
    }
    
    /**
     * 演示无 API Key 时的行为
     */
    private static void demonstrateWithoutApiKey(OpenaiClient client) {
        System.out.println("\n--- 演示无 API Key 时的行为 ---");
        
        String userMessage = "你好，这是一个测试消息。";
        System.out.println("用户: " + userMessage);
        
        // 使用新的 API
        List<ChatInterface.OpenAIMessage> messages = client.generateOpenaiResponse(null, userMessage);
        
        System.out.println("\n返回的消息列表:");
        for (int i = 0; i < messages.size(); i++) {
            ChatInterface.OpenAIMessage msg = messages.get(i);
            System.out.println((i + 1) + ". [" + msg.getRole() + "] " + msg.getContent());
        }
        
        System.out.println("\n可以看到，即使没有 API Key，也返回了完整的消息结构，包括用户消息和错误信息。");
    }
    
    /**
     * 测试新的消息列表 API
     */
    private static void testMessageListAPI(OpenaiClient client) {
        System.out.println("\n--- 测试 1: 新的消息列表 API ---");
        
        String userMessage = "请用一句话介绍 Java 编程语言。";
        System.out.println("用户: " + userMessage);
        
        // 使用新的 API 获取完整消息列表
        List<ChatInterface.OpenAIMessage> messages = client.generateOpenaiResponse(null, userMessage);
        
        System.out.println("\n完整的消息历史:");
        for (int i = 0; i < messages.size(); i++) {
            ChatInterface.OpenAIMessage msg = messages.get(i);
            System.out.println((i + 1) + ". [" + msg.getRole() + "] " + msg.getContent());
            
            // 显示消息的其他属性（如果有）
            if (msg.getToolCalls() != null && !msg.getToolCalls().isEmpty()) {
                System.out.println("   工具调用: " + msg.getToolCalls().size() + " 个");
            }
        }
        
        System.out.println("\n优势: 返回完整的对话结构，便于管理和显示");
    }
    
    /**
     * 测试对话历史管理
     */
    private static void testConversationHistory(OpenaiClient client) {
        System.out.println("\n--- 测试 2: 对话历史管理 ---");
        
        // 初始对话
        List<ChatInterface.OpenAIMessage> conversation = new ArrayList<>();
        
        // 第一轮对话
        System.out.println("第一轮对话:");
        String message1 = "我想学习设计模式，从哪里开始？";
        System.out.println("用户: " + message1);
        
        conversation = client.generateOpenaiResponse(conversation, message1);
        printLatestAssistantMessage(conversation);
        
        // 第二轮对话
        System.out.println("\n第二轮对话:");
        String message2 = "能详细解释一下单例模式吗？";
        System.out.println("用户: " + message2);
        
        conversation = client.generateOpenaiResponse(conversation, message2);
        printLatestAssistantMessage(conversation);
        
        // 显示完整对话历史
        System.out.println("\n完整对话历史 (" + conversation.size() + " 条消息):");
        for (int i = 0; i < conversation.size(); i++) {
            ChatInterface.OpenAIMessage msg = conversation.get(i);
            String preview = msg.getContent().length() > 50 ? 
                msg.getContent().substring(0, 50) + "..." : msg.getContent();
            System.out.println((i + 1) + ". [" + msg.getRole() + "] " + preview);
        }
        
        System.out.println("\n优势: 自动维护完整的对话历史，支持多轮对话");
    }
    
    /**
     * 测试向后兼容性
     */
    private static void testBackwardCompatibility(OpenaiClient client) {
        System.out.println("\n--- 测试 3: 向后兼容性 ---");
        
        String userMessage = "什么是面向对象编程？";
        System.out.println("用户: " + userMessage);
        
        // 使用旧的字符串 API（向后兼容）
        String oldResponse = client.generateOpenaiResponseString(null, userMessage);
        System.out.println("旧 API 返回: " + oldResponse);
        
        // 使用新的消息列表 API
        List<ChatInterface.OpenAIMessage> newResponse = client.generateOpenaiResponse(null, userMessage);
        String newResponseContent = "";
        for (ChatInterface.OpenAIMessage msg : newResponse) {
            if ("assistant".equals(msg.getRole())) {
                newResponseContent = msg.getContent();
                break;
            }
        }
        System.out.println("新 API 返回: " + newResponseContent);
        
        System.out.println("\n优势: 保持向后兼容性，现有代码无需修改");
    }
    
    /**
     * 打印最新的助手消息
     */
    private static void printLatestAssistantMessage(List<ChatInterface.OpenAIMessage> messages) {
        for (int i = messages.size() - 1; i >= 0; i--) {
            ChatInterface.OpenAIMessage msg = messages.get(i);
            if ("assistant".equals(msg.getRole())) {
                System.out.println("AI: " + msg.getContent());
                break;
            }
        }
    }
}
