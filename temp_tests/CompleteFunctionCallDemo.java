package com.example.chat;

import com.example.chat.service.*;
import com.example.chat.tools.DirectoryListTool;
import com.example.chat.tools.SystemInfoTool;

import java.util.HashMap;
import java.util.Map;

/**
 * 完整的Function Call功能演示
 * 展示多个工具的注册和使用
 */
public class CompleteFunctionCallDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 完整Function Call功能演示 ===\n");
        
        // 创建Function Call管理器
        FunctionCallManager manager = new DefaultFunctionCallManager();
        
        // 注册多个工具
        registerTools(manager);
        
        // 演示工具功能
        demonstrateTools(manager);
        
        // 显示OpenAI集成信息
        showOpenAIIntegration(manager);
        
        System.out.println("\n=== 演示完成 ===");
    }
    
    /**
     * 注册工具
     */
    private static void registerTools(FunctionCallManager manager) {
        System.out.println("1. 注册工具");
        
        // 注册目录列表工具
        Tool directoryTool = DirectoryListTool.createTool();
        manager.registerTool(directoryTool);
        
        // 注册系统信息工具
        Tool systemTool = SystemInfoTool.createTool();
        manager.registerTool(systemTool);
        
        System.out.println("已注册工具数量: " + manager.getRegisteredTools().size());
        for (Tool tool : manager.getRegisteredTools()) {
            System.out.println("  - " + tool.getName() + ": " + tool.getDescription());
        }
        System.out.println();
    }
    
    /**
     * 演示工具功能
     */
    private static void demonstrateTools(FunctionCallManager manager) {
        System.out.println("2. 演示工具功能\n");
        
        // 演示目录列表工具
        demonstrateDirectoryTool(manager);
        
        // 演示系统信息工具
        demonstrateSystemTool(manager);
    }
    
    /**
     * 演示目录列表工具
     */
    private static void demonstrateDirectoryTool(FunctionCallManager manager) {
        System.out.println("📁 目录列表工具演示:");
        
        try {
            // 简单列表
            Map<String, Object> args1 = new HashMap<>();
            args1.put("path", ".");
            args1.put("detailed", false);
            
            ToolExecutionResult result1 = manager.callTool("list_directory", args1);
            System.out.println("当前目录内容:");
            System.out.println(result1.getFormattedResult());
            System.out.println("执行耗时: " + result1.getExecutionDurationMs() + "ms\n");
            
            // 详细列表
            Map<String, Object> args2 = new HashMap<>();
            args2.put("path", "src");
            args2.put("detailed", true);
            args2.put("sort_by", "size");
            
            ToolExecutionResult result2 = manager.callTool("list_directory", args2);
            System.out.println("src目录详细信息:");
            System.out.println(result2.getFormattedResult());
            System.out.println("执行耗时: " + result2.getExecutionDurationMs() + "ms\n");
            
        } catch (FunctionCallManager.ToolExecutionException e) {
            System.err.println("目录工具执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 演示系统信息工具
     */
    private static void demonstrateSystemTool(FunctionCallManager manager) {
        System.out.println("🖥️ 系统信息工具演示:");
        
        try {
            // 获取操作系统信息
            Map<String, Object> args1 = new HashMap<>();
            args1.put("info_type", "os");
            
            ToolExecutionResult result1 = manager.callTool("get_system_info", args1);
            System.out.println(result1.getFormattedResult());
            System.out.println("执行耗时: " + result1.getExecutionDurationMs() + "ms\n");
            
            // 获取内存信息
            Map<String, Object> args2 = new HashMap<>();
            args2.put("info_type", "memory");
            
            ToolExecutionResult result2 = manager.callTool("get_system_info", args2);
            System.out.println(result2.getFormattedResult());
            System.out.println("执行耗时: " + result2.getExecutionDurationMs() + "ms\n");
            
            // 获取运行时信息
            Map<String, Object> args3 = new HashMap<>();
            args3.put("info_type", "runtime");
            
            ToolExecutionResult result3 = manager.callTool("get_system_info", args3);
            System.out.println(result3.getFormattedResult());
            System.out.println("执行耗时: " + result3.getExecutionDurationMs() + "ms\n");
            
        } catch (FunctionCallManager.ToolExecutionException e) {
            System.err.println("系统信息工具执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 显示OpenAI集成信息
     */
    private static void showOpenAIIntegration(FunctionCallManager manager) {
        System.out.println("3. OpenAI集成信息\n");
        
        System.out.println("OpenAI函数定义数量: " + manager.getOpenAIFunctionDefinitions().size());
        System.out.println("\n这些工具可以通过以下方式在ChatInterface中使用:");
        System.out.println("1. 用户发送自然语言请求");
        System.out.println("2. OpenAI API识别需要调用的工具");
        System.out.println("3. FunctionCallManager执行相应工具");
        System.out.println("4. 工具结果返回给AI生成最终回复");
        
        System.out.println("\n示例用户请求:");
        System.out.println("  \"请列出当前目录的文件\"");
        System.out.println("  \"显示系统内存使用情况\"");
        System.out.println("  \"查看src文件夹的详细信息\"");
        System.out.println("  \"获取操作系统信息\"");
        
        System.out.println("\n集成代码示例:");
        System.out.println("```java");
        System.out.println("// 创建管理器并注册工具");
        System.out.println("FunctionCallManager manager = new DefaultFunctionCallManager();");
        System.out.println("manager.registerTool(DirectoryListTool.createTool());");
        System.out.println("manager.registerTool(SystemInfoTool.createTool());");
        System.out.println("");
        System.out.println("// 设置到OpenAI客户端");
        System.out.println("OpenaiClient client = new OpenaiClient();");
        System.out.println("client.setFunctionCallManager(manager);");
        System.out.println("");
        System.out.println("// 在ChatInterface中使用");
        System.out.println("chatInterface.onMessageSent(userMessage -> {");
        System.out.println("    List<Message> response = client.generateOpenaiResponse(history, userMessage);");
        System.out.println("    chatInterface.setMessages(response);");
        System.out.println("});");
        System.out.println("```");
    }
}
