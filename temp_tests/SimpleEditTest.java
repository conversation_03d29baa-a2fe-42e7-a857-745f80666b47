package com.example.chat;

import javax.swing.SwingUtilities;
import java.util.ArrayList;
import java.util.List;

/**
 * 简化的消息编辑功能测试
 */
public class SimpleEditTest {
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // 创建聊天助手实例
            CopilotChatAssistant chatAssistant = new CopilotChatAssistant();
            
            // 注册消息编辑监听器
            chatAssistant.onMessageEdited((messageIndex, newContent) -> {
                System.out.println("=== 消息编辑事件 ===");
                System.out.println("消息索引: " + messageIndex);
                System.out.println("新内容: " + newContent);
                System.out.println("==================");
            });
            
            // 注册删除消息监听器
            chatAssistant.onDeleteMessages(messageIndices -> {
                System.out.println("=== 消息删除事件 ===");
                System.out.println("删除的消息索引: " + messageIndices);
                System.out.println("==================");
            });
            
            // 注册消息发送监听器
            chatAssistant.onMessageSent(userMessage -> {
                System.out.println("=== 消息发送事件 ===");
                System.out.println("用户消息: " + userMessage);
                System.out.println("==================");
            });
            
            // 设置初始消息
            setInitialMessages(chatAssistant);
            
            // 显示界面
            chatAssistant.setVisible(true);
            
            System.out.println("=== 测试说明 ===");
            System.out.println("1. 将鼠标悬停在任何消息上");
            System.out.println("2. 点击 ✏️ 按钮编辑消息");
            System.out.println("3. 点击 📋 按钮复制消息");
            System.out.println("4. 点击 🗑 按钮删除消息");
            System.out.println("5. 查看控制台输出的事件信息");
            System.out.println("===============");
        });
    }
    
    /**
     * 设置初始消息
     */
    private static void setInitialMessages(CopilotChatAssistant chatAssistant) {
        // 创建简单的消息对象，不依赖Jackson
        List<SimpleMessage> messages = new ArrayList<>();
        
        // 系统消息
        messages.add(new SimpleMessage("system", 
            "You are GitHub Copilot, an AI programming assistant."));
        
        // 欢迎消息
        messages.add(new SimpleMessage("assistant", 
            "Hi! I'm GitHub Copilot. 🤖\n\n" +
            "New features available:\n" +
            "• ✏️ Edit any message\n" +
            "• 📋 Copy message content\n" +
            "• 🗑 Delete messages\n\n" +
            "Try hovering over this message and click the edit button!"));
        
        // 用户消息示例
        messages.add(new SimpleMessage("user", 
            "This is a sample user message. You can edit it by clicking the ✏️ button!"));
        
        // 另一个助手消息
        messages.add(new SimpleMessage("assistant", 
            "Perfect! You can edit any message in this conversation. " +
            "When you edit a message, the callback will be triggered with the message index and new content. " +
            "This is useful for implementing features like message history management and conversation editing."));
        
        // 转换为OpenAI格式
        List<com.example.chat.model.OpenAIRequest.Message> openAIMessages = new ArrayList<>();
        for (SimpleMessage msg : messages) {
            com.example.chat.model.OpenAIRequest.Message openAIMsg = 
                new com.example.chat.model.OpenAIRequest.Message();
            openAIMsg.setRole(msg.role);
            openAIMsg.setContent(msg.content);
            openAIMessages.add(openAIMsg);
        }
        
        chatAssistant.setMessages(openAIMessages);
    }
    
    /**
     * 简单的消息类，用于测试
     */
    private static class SimpleMessage {
        final String role;
        final String content;
        
        SimpleMessage(String role, String content) {
            this.role = role;
            this.content = content;
        }
    }
}
