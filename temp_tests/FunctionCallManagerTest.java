package com.example.chat;

import com.example.chat.service.*;
import com.example.chat.tools.DirectoryListTool;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Function Call 管理器测试
 */
public class FunctionCallManagerTest {
    
    public static void main(String[] args) {
        System.out.println("=== Function Call Manager 测试 ===\n");
        
        // 创建管理器
        FunctionCallManager manager = new DefaultFunctionCallManager();
        
        // 测试工具注册
        testToolRegistration(manager);
        
        // 测试工具调用
        testToolExecution(manager);
        
        // 测试OpenAI函数定义
        testOpenAIFunctionDefinitions(manager);
        
        // 测试错误处理
        testErrorHandling(manager);
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 测试工具注册
     */
    private static void testToolRegistration(FunctionCallManager manager) {
        System.out.println("1. 测试工具注册");
        
        // 注册目录列表工具
        Tool directoryTool = DirectoryListTool.createTool();
        manager.registerTool(directoryTool);
        
        // 验证注册
        System.out.println("已注册工具数量: " + manager.getRegisteredTools().size());
        System.out.println("工具是否已注册: " + manager.isToolRegistered("list_directory"));
        
        // 获取工具详情
        Tool tool = manager.getTool("list_directory");
        if (tool != null) {
            System.out.println("工具详情: " + tool);
            System.out.println("参数数量: " + tool.getParameters().size());
            for (Tool.ToolParameter param : tool.getParameters()) {
                System.out.println("  - " + param.getName() + " (" + param.getType() + "): " + param.getDescription());
            }
        }
        
        System.out.println();
    }
    
    /**
     * 测试工具调用
     */
    private static void testToolExecution(FunctionCallManager manager) {
        System.out.println("2. 测试工具调用");
        
        try {
            // 测试1: 简单调用（当前目录）
            System.out.println("测试1: 列出当前目录");
            Map<String, Object> args1 = new HashMap<>();
            args1.put("path", ".");
            args1.put("show_hidden", false);
            args1.put("detailed", false);
            
            ToolExecutionResult result1 = manager.callTool("list_directory", args1);
            System.out.println("执行结果:");
            System.out.println(result1.getFormattedResult());
            System.out.println("执行耗时: " + result1.getExecutionDurationMs() + "ms\n");
            
            // 测试2: 详细信息调用
            System.out.println("测试2: 列出当前目录（详细信息）");
            Map<String, Object> args2 = new HashMap<>();
            args2.put("path", ".");
            args2.put("show_hidden", false);
            args2.put("detailed", true);
            args2.put("sort_by", "size");
            
            ToolExecutionResult result2 = manager.callTool("list_directory", args2);
            System.out.println("执行结果:");
            System.out.println(result2.getFormattedResult());
            System.out.println("执行耗时: " + result2.getExecutionDurationMs() + "ms\n");
            
            // 测试3: JSON字符串参数调用
            System.out.println("测试3: 使用JSON字符串参数");
            String jsonArgs = "{\"path\":\"src\",\"show_hidden\":false,\"detailed\":false,\"sort_by\":\"name\"}";
            
            ToolExecutionResult result3 = manager.callTool("list_directory", jsonArgs);
            System.out.println("执行结果:");
            System.out.println(result3.getFormattedResult());
            System.out.println("执行耗时: " + result3.getExecutionDurationMs() + "ms\n");
            
        } catch (FunctionCallManager.ToolExecutionException e) {
            System.err.println("工具执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试OpenAI函数定义
     */
    private static void testOpenAIFunctionDefinitions(FunctionCallManager manager) {
        System.out.println("3. 测试OpenAI函数定义");
        
        List<Map<String, Object>> definitions = manager.getOpenAIFunctionDefinitions();
        System.out.println("函数定义数量: " + definitions.size());
        
        for (Map<String, Object> definition : definitions) {
            System.out.println("函数定义:");
            printMap(definition, 0);
        }
        
        System.out.println();
    }
    
    /**
     * 测试错误处理
     */
    private static void testErrorHandling(FunctionCallManager manager) {
        System.out.println("4. 测试错误处理");
        
        try {
            // 测试1: 调用不存在的工具
            System.out.println("测试1: 调用不存在的工具");
            manager.callTool("non_existent_tool", new HashMap<>());
        } catch (FunctionCallManager.ToolExecutionException e) {
            System.out.println("预期错误: " + e.getErrorCode() + " - " + e.getMessage());
        }
        
        try {
            // 测试2: 无效路径
            System.out.println("测试2: 无效路径");
            Map<String, Object> args = new HashMap<>();
            args.put("path", "/non/existent/path");
            
            ToolExecutionResult result = manager.callTool("list_directory", args);
            if (!result.isSuccess()) {
                System.out.println("预期错误: " + result.getErrorCode() + " - " + result.getErrorMessage());
            }
        } catch (FunctionCallManager.ToolExecutionException e) {
            System.out.println("预期错误: " + e.getErrorCode() + " - " + e.getMessage());
        }
        
        try {
            // 测试3: 无效JSON参数
            System.out.println("测试3: 无效JSON参数");
            manager.callTool("list_directory", "{invalid json}");
        } catch (FunctionCallManager.ToolExecutionException e) {
            System.out.println("预期错误: " + e.getErrorCode() + " - " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 递归打印Map结构
     */
    private static void printMap(Map<String, Object> map, int indent) {
        String indentStr = "  ".repeat(indent);
        
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (value instanceof Map) {
                System.out.println(indentStr + key + ":");
                @SuppressWarnings("unchecked")
                Map<String, Object> nestedMap = (Map<String, Object>) value;
                printMap(nestedMap, indent + 1);
            } else if (value instanceof List) {
                System.out.println(indentStr + key + ": " + value);
            } else {
                System.out.println(indentStr + key + ": " + value);
            }
        }
    }
}
