package com.example.chat.service;

import com.example.chat.ChatInterface;

import java.util.ArrayList;
import java.util.List;

/**
 * OpenAI 客户端测试类
 */
public class OpenAIClientTest {
    
    public static void main(String[] args) {
        System.out.println("=== OpenAI 客户端测试 ===");
        
        // 创建客户端实例
        OpenaiClient client = new OpenaiClient();
        
        // 检查 API Key
        if (!client.isApiKeyValid()) {
            System.err.println("错误：OpenAI API Key 未配置或无效！");
            System.err.println("请设置环境变量：export OPENAI_API_KEY=sk-your-key-here");
            System.err.println("或者在代码中设置：client.setApiKey(\"sk-your-key-here\");");
            
            // 演示如何在代码中设置 API Key（仅用于测试）
            System.out.println("\n如果你想在代码中设置 API Key，请取消注释下面的行：");
            System.out.println("// client.setApiKey(\"sk-your-actual-api-key-here\");");
            
            return;
        }
        
        System.out.println("API Key 验证通过，开始测试...");
        
        // 测试简单对话
        testSimpleConversation(client);
        
        // 测试带历史记录的对话
        testConversationWithHistory(client);
    }
    
    /**
     * 测试简单对话
     */
    private static void testSimpleConversation(OpenaiClient client) {
        System.out.println("\n--- 测试 1: 简单对话 ---");

        String userMessage = "你好，请简单介绍一下你自己。";
        System.out.println("用户: " + userMessage);

        // 使用新的 API 获取完整消息列表
        List<ChatInterface.OpenAIMessage> messages = client.generateOpenaiResponse(null, userMessage);

        // 打印所有消息
        for (ChatInterface.OpenAIMessage msg : messages) {
            System.out.println(msg.getRole() + ": " + msg.getContent());
        }

        // 或者使用兼容性方法获取字符串响应
        System.out.println("\n使用兼容性方法:");
        String response = client.generateOpenaiResponseString(null, userMessage);
        System.out.println("AI: " + response);
    }
    
    /**
     * 测试带历史记录的对话
     */
    private static void testConversationWithHistory(OpenaiClient client) {
        System.out.println("\n--- 测试 2: 带历史记录的对话 ---");

        // 构建历史消息
        List<ChatInterface.OpenAIMessage> history = new ArrayList<>();

        // 系统消息
        history.add(new ChatInterface.OpenAIMessage("system",
            "你是一个有用的编程助手，专门帮助开发者解决技术问题。"));

        // 历史对话
        history.add(new ChatInterface.OpenAIMessage("user",
            "什么是设计模式？"));

        history.add(new ChatInterface.OpenAIMessage("assistant",
            "设计模式是软件工程中针对常见问题的可重用解决方案。它们是经过验证的最佳实践，" +
            "可以帮助开发者写出更清晰、更灵活、更易维护的代码。主要分为创建型、结构型和行为型三大类。"));

        // 新的用户消息
        String newUserMessage = "能给我举个单例模式的例子吗？";
        System.out.println("用户: " + newUserMessage);

        // 使用新的 API 获取完整消息列表
        List<ChatInterface.OpenAIMessage> updatedMessages = client.generateOpenaiResponse(history, newUserMessage);

        // 打印新增的消息（用户消息和AI回复）
        System.out.println("\n完整对话历史:");
        for (int i = Math.max(0, updatedMessages.size() - 2); i < updatedMessages.size(); i++) {
            ChatInterface.OpenAIMessage msg = updatedMessages.get(i);
            System.out.println(msg.getRole() + ": " + msg.getContent());
        }

        System.out.println("\n总消息数: " + updatedMessages.size());
    }
}
