package com.example.chat;

import javax.swing.SwingUtilities;
import javax.swing.Timer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Function Call 消息测试类
 */
public class FunctionCallTest {
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // 创建聊天助手实例
            CopilotChatAssistant chatAssistant = new CopilotChatAssistant();
            
            // 注册消息发送监听器
            chatAssistant.onMessageSent(userMessage -> {
                System.out.println("用户发送了消息: " + userMessage);
                handleUserMessage(chatAssistant, userMessage);
            });
            
            // 设置包含 function call 的消息示例
            setFunctionCallExample(chatAssistant);
            
            // 显示界面
            chatAssistant.setVisible(true);
        });
    }
    
    /**
     * 设置包含 function call 的消息示例
     */
    private static void setFunctionCallExample(ChatInterface chatInterface) {
        List<ChatInterface.OpenAIMessage> messages = new ArrayList<>();
        
        // 1. 系统消息
        messages.add(new ChatInterface.OpenAIMessage("system", 
            "You are a helpful assistant with access to various tools."));
        
        // 2. 用户请求
        messages.add(new ChatInterface.OpenAIMessage("user", 
            "What's the weather like in Beijing? And can you search for recent news about AI?"));
        
        // 3. Assistant 响应，包含工具调用
        ChatInterface.OpenAIMessage assistantMsg = new ChatInterface.OpenAIMessage("assistant", 
            "I'll help you get the weather information for Beijing and search for recent AI news.");
        
        // 创建工具调用
        List<ChatInterface.ToolCall> toolCalls = Arrays.asList(
            new ChatInterface.ToolCall("call_1", "function", 
                new ChatInterface.FunctionCall("get_weather", 
                    "{\"location\": \"Beijing\", \"unit\": \"celsius\"}")),
            new ChatInterface.ToolCall("call_2", "function", 
                new ChatInterface.FunctionCall("search_news", 
                    "{\"query\": \"artificial intelligence\", \"category\": \"technology\", \"limit\": 5}"))
        );
        
        assistantMsg.setToolCalls(toolCalls);
        messages.add(assistantMsg);
        
        // 4. 函数执行结果 1
        ChatInterface.OpenAIMessage weatherResult = new ChatInterface.OpenAIMessage("tool", 
            "Current weather in Beijing:\n" +
            "Temperature: 22°C\n" +
            "Condition: Partly cloudy\n" +
            "Humidity: 65%\n" +
            "Wind: 8 km/h NE");
        weatherResult.setToolCallId("call_1");
        weatherResult.setName("get_weather");
        messages.add(weatherResult);
        
        // 5. 函数执行结果 2
        ChatInterface.OpenAIMessage newsResult = new ChatInterface.OpenAIMessage("tool", 
            "Recent AI News:\n\n" +
            "1. OpenAI releases new GPT model with improved reasoning\n" +
            "2. Google announces breakthrough in quantum AI computing\n" +
            "3. Meta unveils new AI assistant for enterprise\n" +
            "4. Microsoft integrates AI tools into Office suite\n" +
            "5. Tesla expands AI usage in autonomous driving");
        newsResult.setToolCallId("call_2");
        newsResult.setName("search_news");
        messages.add(newsResult);
        
        // 6. Assistant 最终回复
        messages.add(new ChatInterface.OpenAIMessage("assistant", 
            "Based on the information I gathered:\n\n" +
            "🌤️ **Weather in Beijing:**\n" +
            "It's currently 22°C with partly cloudy skies. The humidity is at 65% with a gentle northeast wind.\n\n" +
            "📰 **Recent AI News:**\n" +
            "There's been quite a bit of activity in the AI space recently! OpenAI has released a new model, " +
            "Google made quantum AI breakthroughs, and major tech companies like Meta, Microsoft, and Tesla " +
            "are expanding their AI capabilities.\n\n" +
            "Is there any specific aspect of the weather or AI news you'd like me to elaborate on?"));
        
        chatInterface.setMessages(messages);
    }
    
    /**
     * 处理用户消息（简化版本）
     */
    private static void handleUserMessage(ChatInterface chatInterface, String userMessage) {
        chatInterface.setReplyingStatus(true);
        
        Timer timer = new Timer(1500, e -> {
            // 模拟简单回复
            List<ChatInterface.OpenAIMessage> messages = new ArrayList<>();
            messages.add(new ChatInterface.OpenAIMessage("user", userMessage));
            messages.add(new ChatInterface.OpenAIMessage("assistant", 
                "I understand you're asking about: \"" + userMessage + "\"\n\n" +
                "For a demonstration of function calls, please check the initial conversation above. " +
                "This shows how I can call multiple tools simultaneously and present their results in a structured way."));
            
            chatInterface.setMessages(messages);
            chatInterface.setReplyingStatus(false);
        });
        timer.setRepeats(false);
        timer.start();
    }
}