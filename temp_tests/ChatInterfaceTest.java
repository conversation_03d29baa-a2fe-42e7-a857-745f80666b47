package com.example.chat;

import javax.swing.SwingUtilities;
import javax.swing.Timer;
import java.util.ArrayList;
import java.util.List;

/**
 * ChatInterface 接口功能测试类
 */
public class ChatInterfaceTest {
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // 创建聊天助手实例
            CopilotChatAssistant chatAssistant = new CopilotChatAssistant();
            
            // 注册消息发送监听器
            chatAssistant.onMessageSent(userMessage -> {
                System.out.println("用户发送了消息: " + userMessage);
                
                // 模拟处理用户消息，然后更新UI
                handleUserMessage(chatAssistant, userMessage);
            });
            
            // 设置初始消息
            setInitialMessages(chatAssistant);
            
            // 显示界面
            chatAssistant.setVisible(true);
        });
    }
    
    /**
     * 设置初始消息
     */
    private static void setInitialMessages(ChatInterface chatInterface) {
        List<ChatInterface.OpenAIMessage> messages = new ArrayList<>();
        
        // 系统消息
        messages.add(new ChatInterface.OpenAIMessage("system", 
            "You are G<PERSON><PERSON><PERSON> Copilot, an AI programming assistant."));
        
        // 欢迎消息
        messages.add(new ChatInterface.OpenAIMessage("assistant", 
            "Hi! I'm GitHub Copilot, your AI pair programmer. I'm here to help you with:\n\n" +
            "• Writing and completing code\n" +
            "• Explaining complex code sections\n" +
            "• Debugging and fixing issues\n" +
            "• Suggesting best practices\n" +
            "• Generating tests and documentation\n\n" +
            "What can I help you build today?"));
        
        chatInterface.setMessages(messages);
    }
    
    /**
     * 处理用户消息并模拟AI回复
     */
    private static void handleUserMessage(ChatInterface chatInterface, String userMessage) {
        // 1. 设置AI正在回复状态
        chatInterface.setReplyingStatus(true);
        
        // 2. 获取当前消息列表（实际应用中你会维护完整的消息历史）
        List<ChatInterface.OpenAIMessage> messages = new ArrayList<>();
        
        // 添加系统消息
        messages.add(new ChatInterface.OpenAIMessage("system", 
            "You are GitHub Copilot, an AI programming assistant."));
        
        // 添加用户消息
        messages.add(new ChatInterface.OpenAIMessage("user", userMessage));
        
        // 3. 更新UI显示用户消息
        chatInterface.setMessages(messages);
        
        // 4. 模拟API调用延迟（1-3秒）
        Timer timer = new Timer(1000 + (int)(Math.random() * 2000), e -> {
            // 5. 模拟AI回复
            String aiResponse = generateAIResponse(userMessage);
            messages.add(new ChatInterface.OpenAIMessage("assistant", aiResponse));
            
            // 6. 更新UI显示AI回复
            chatInterface.setMessages(messages);
            
            // 7. 取消回复状态
            chatInterface.setReplyingStatus(false);
        });
        timer.setRepeats(false);
        timer.start();
    }
    
    /**
     * 模拟生成AI回复
     */
    private static String generateAIResponse(String userMessage) {
        String msg = userMessage.toLowerCase();
        
        if (msg.contains("hello") || msg.contains("hi") || msg.contains("你好")) {
            return "Hello! How can I help you with your code today?";
        }
        
        if (msg.contains("java")) {
            return "I can help you with Java development! What specific Java topic would you like assistance with?";
        }
        
        if (msg.contains("spring")) {
            return "Spring is a great framework! I can help with:\n\n" +
                   "• Spring Boot applications\n" +
                   "• REST APIs\n" +
                   "• Dependency injection\n" +
                   "• Data access with JPA\n" +
                   "• Security configuration\n\n" +
                   "What would you like to work on?";
        }
        
        if (msg.contains("bug") || msg.contains("error")) {
            return "I'd be happy to help debug! Please share:\n\n" +
                   "• The error message\n" +
                   "• Your code snippet\n" +
                   "• What you expected to happen\n\n" +
                   "I'll help you identify and fix the issue.";
        }
        
        // 默认回复
        return "I understand you're asking about: \"" + userMessage + "\"\n\n" +
               "Could you provide more details? I'm here to help with programming tasks, " +
               "code explanations, debugging, and development best practices.";
    }
}