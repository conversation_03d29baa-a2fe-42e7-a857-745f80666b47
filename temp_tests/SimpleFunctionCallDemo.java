package com.example.chat;

import com.example.chat.service.*;
import com.example.chat.tools.DirectoryListTool;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 简单的Function Call功能演示
 */
public class SimpleFunctionCallDemo {
    
    public static void main(String[] args) {
        System.out.println("=== Function Call 功能演示 ===\n");
        
        // 1. 创建Function Call管理器
        System.out.println("1. 创建Function Call管理器");
        FunctionCallManager manager = new DefaultFunctionCallManager();
        
        // 2. 注册工具
        System.out.println("2. 注册目录列表工具");
        Tool directoryTool = DirectoryListTool.createTool();
        manager.registerTool(directoryTool);
        
        System.out.println("已注册工具: " + directoryTool.getName());
        System.out.println("工具描述: " + directoryTool.getDescription());
        System.out.println("参数数量: " + directoryTool.getParameters().size());
        
        // 3. 显示工具参数
        System.out.println("\n3. 工具参数详情:");
        for (Tool.ToolParameter param : directoryTool.getParameters()) {
            System.out.println("  - " + param.getName() + " (" + param.getType() + "): " + param.getDescription());
            System.out.println("    必需: " + param.isRequired() + ", 默认值: " + param.getDefaultValue());
        }
        
        // 4. 测试工具调用
        System.out.println("\n4. 测试工具调用:");
        
        try {
            // 测试1: 列出当前目录
            System.out.println("\n测试1: 列出当前目录");
            Map<String, Object> args1 = new HashMap<>();
            args1.put("path", ".");
            args1.put("detailed", false);
            
            ToolExecutionResult result1 = manager.callTool("list_directory", args1);
            System.out.println("执行成功: " + result1.isSuccess());
            System.out.println("执行耗时: " + result1.getExecutionDurationMs() + "ms");
            System.out.println("结果:\n" + result1.getFormattedResult());
            
            // 测试2: 列出src目录（详细信息）
            System.out.println("\n" + "=".repeat(50));
            System.out.println("测试2: 列出src目录（详细信息）");
            Map<String, Object> args2 = new HashMap<>();
            args2.put("path", "src");
            args2.put("detailed", true);
            args2.put("sort_by", "size");
            
            ToolExecutionResult result2 = manager.callTool("list_directory", args2);
            System.out.println("执行成功: " + result2.isSuccess());
            System.out.println("执行耗时: " + result2.getExecutionDurationMs() + "ms");
            System.out.println("结果:\n" + result2.getFormattedResult());
            
        } catch (FunctionCallManager.ToolExecutionException e) {
            System.err.println("工具执行失败: " + e.getErrorCode() + " - " + e.getMessage());
            e.printStackTrace();
        }
        
        // 5. 显示OpenAI函数定义
        System.out.println("\n5. OpenAI函数定义:");
        List<Map<String, Object>> definitions = manager.getOpenAIFunctionDefinitions();
        System.out.println("函数定义数量: " + definitions.size());
        
        System.out.println("\n=== 演示完成 ===");
        System.out.println("\n实际使用说明:");
        System.out.println("1. 在ChatInterface中集成FunctionCallManager");
        System.out.println("2. OpenaiClient会自动处理工具调用");
        System.out.println("3. 用户可以自然语言请求文件操作");
        System.out.println("4. AI会自动调用相应工具并返回结果");
    }
}
