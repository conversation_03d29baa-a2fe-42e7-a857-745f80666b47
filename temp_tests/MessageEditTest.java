package com.example.chat;

import javax.swing.SwingUtilities;
import javax.swing.Timer;
import java.util.ArrayList;
import java.util.List;

/**
 * 测试消息编辑功能
 */
public class MessageEditTest {
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // 创建聊天助手实例
            CopilotChatAssistant chatAssistant = new CopilotChatAssistant();
            
            // 注册消息发送监听器
            chatAssistant.onMessageSent(userMessage -> {
                System.out.println("用户发送了消息: " + userMessage);
                handleUserMessage(chatAssistant, userMessage);
            });
            
            // 注册消息编辑监听器
            chatAssistant.onMessageEdited((messageIndex, newContent) -> {
                System.out.println("用户编辑了消息 [索引: " + messageIndex + "]: " + newContent);
                handleMessageEdit(chatAssistant, messageIndex, newContent);
            });
            
            // 注册删除消息监听器
            chatAssistant.onDeleteMessages(messageIndices -> {
                System.out.println("用户删除了消息，索引: " + messageIndices);
                handleDeleteMessages(chatAssistant, messageIndices);
            });
            
            // 设置初始消息
            setInitialMessages(chatAssistant);
            
            // 显示界面
            chatAssistant.setVisible(true);
        });
    }
    
    /**
     * 设置初始消息
     */
    private static void setInitialMessages(ChatInterface chatInterface) {
        List<com.example.chat.model.OpenAIRequest.Message> messages = new ArrayList<>();

        // 系统消息
        messages.add(new com.example.chat.model.OpenAIRequest.Message("system",
            "You are GitHub Copilot, an AI programming assistant."));

        // 欢迎消息
        messages.add(new com.example.chat.model.OpenAIRequest.Message("assistant",
            "Hi! I'm GitHub Copilot. You can now:\n\n" +
            "• Send messages\n" +
            "• Edit any message by clicking the ✏️ button\n" +
            "• Copy messages with 📋\n" +
            "• Delete messages with 🗑\n\n" +
            "Try editing this message to test the functionality!"));

        // 用户消息示例
        messages.add(new com.example.chat.model.OpenAIRequest.Message("user",
            "This is a sample user message. You can edit it too!"));

        chatInterface.setMessages(messages);
    }
    
    /**
     * 处理用户消息并模拟AI回复
     */
    private static void handleUserMessage(ChatInterface chatInterface, String userMessage) {
        // 设置AI正在回复状态
        chatInterface.setReplyingStatus(true);
        
        // 模拟API调用延迟
        Timer timer = new Timer(1500, e -> {
            // 模拟AI回复
            List<com.example.chat.model.OpenAIRequest.Message> updatedMessages = new ArrayList<>();

            // 添加系统消息
            updatedMessages.add(new com.example.chat.model.OpenAIRequest.Message("system",
                "You are GitHub Copilot, an AI programming assistant."));

            // 添加欢迎消息
            updatedMessages.add(new com.example.chat.model.OpenAIRequest.Message("assistant",
                "Hi! I'm GitHub Copilot. You can now:\n\n" +
                "• Send messages\n" +
                "• Edit any message by clicking the ✏️ button\n" +
                "• Copy messages with 📋\n" +
                "• Delete messages with 🗑\n\n" +
                "Try editing this message to test the functionality!"));

            // 添加示例用户消息
            updatedMessages.add(new com.example.chat.model.OpenAIRequest.Message("user",
                "This is a sample user message. You can edit it too!"));

            // 添加用户新消息
            updatedMessages.add(new com.example.chat.model.OpenAIRequest.Message("user", userMessage));

            // 添加AI回复
            updatedMessages.add(new com.example.chat.model.OpenAIRequest.Message("assistant",
                "I received your message: \"" + userMessage + "\"\n\n" +
                "You can edit any message in this conversation by hovering over it and clicking the ✏️ edit button. " +
                "When you edit a message, the callback will be triggered with the message index and new content."));

            chatInterface.setMessages(updatedMessages);
            chatInterface.setReplyingStatus(false);
        });
        timer.setRepeats(false);
        timer.start();
    }
    
    /**
     * 处理消息编辑
     */
    private static void handleMessageEdit(ChatInterface chatInterface, int messageIndex, String newContent) {
        System.out.println("处理消息编辑:");
        System.out.println("  消息索引: " + messageIndex);
        System.out.println("  新内容: " + newContent);
        
        // 在实际应用中，这里你会：
        // 1. 更新你的消息数据模型
        // 2. 可能需要重新调用AI API（如果编辑的是用户消息）
        // 3. 刷新UI显示
        
        // 为了演示，我们简单地显示一个确认消息
        Timer timer = new Timer(500, e -> {
            List<com.example.chat.model.OpenAIRequest.Message> updatedMessages = new ArrayList<>();

            // 添加系统消息
            updatedMessages.add(new com.example.chat.model.OpenAIRequest.Message("system",
                "You are GitHub Copilot, an AI programming assistant."));

            // 添加欢迎消息
            updatedMessages.add(new com.example.chat.model.OpenAIRequest.Message("assistant",
                "Hi! I'm GitHub Copilot. You can now:\n\n" +
                "• Send messages\n" +
                "• Edit any message by clicking the ✏️ button\n" +
                "• Copy messages with 📋\n" +
                "• Delete messages with 🗑\n\n" +
                "Try editing this message to test the functionality!"));

            // 添加示例用户消息
            updatedMessages.add(new com.example.chat.model.OpenAIRequest.Message("user",
                "This is a sample user message. You can edit it too!"));

            // 添加编辑确认消息
            updatedMessages.add(new com.example.chat.model.OpenAIRequest.Message("assistant",
                "✅ Message edited successfully!\n\n" +
                "Message index: " + messageIndex + "\n" +
                "New content: \"" + newContent + "\"\n\n" +
                "In a real application, you would update your data model and possibly regenerate AI responses."));

            chatInterface.setMessages(updatedMessages);
        });
        timer.setRepeats(false);
        timer.start();
    }
    
    /**
     * 处理删除消息
     */
    private static void handleDeleteMessages(ChatInterface chatInterface, List<Integer> messageIndices) {
        System.out.println("处理删除消息: " + messageIndices);
        
        // 简单演示 - 显示删除确认
        Timer timer = new Timer(500, e -> {
            List<com.example.chat.model.OpenAIRequest.Message> updatedMessages = new ArrayList<>();

            // 添加系统消息
            updatedMessages.add(new com.example.chat.model.OpenAIRequest.Message("system",
                "You are GitHub Copilot, an AI programming assistant."));

            // 添加欢迎消息
            updatedMessages.add(new com.example.chat.model.OpenAIRequest.Message("assistant",
                "Hi! I'm GitHub Copilot. You can now:\n\n" +
                "• Send messages\n" +
                "• Edit any message by clicking the ✏️ button\n" +
                "• Copy messages with 📋\n" +
                "• Delete messages with 🗑\n\n" +
                "Try editing this message to test the functionality!"));

            // 添加删除确认消息
            updatedMessages.add(new com.example.chat.model.OpenAIRequest.Message("assistant",
                "🗑️ Messages deleted successfully!\n\n" +
                "Deleted message indices: " + messageIndices + "\n\n" +
                "In a real application, you would remove these messages from your data model."));

            chatInterface.setMessages(updatedMessages);
        });
        timer.setRepeats(false);
        timer.start();
    }
}
