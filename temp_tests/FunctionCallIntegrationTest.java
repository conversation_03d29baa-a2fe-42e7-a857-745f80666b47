package com.example.chat;

import com.example.chat.service.*;
import com.example.chat.tools.DirectoryListTool;

import javax.swing.SwingUtilities;
import javax.swing.Timer;
import java.util.ArrayList;
import java.util.List;

/**
 * Function Call 集成测试
 * 演示ChatInterface与Function Call的完整集成
 */
public class FunctionCallIntegrationTest {
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // 创建聊天助手实例
            CopilotChatAssistant chatAssistant = new CopilotChatAssistant();
            
            // 创建Function Call管理器并注册工具
            FunctionCallManager functionManager = new DefaultFunctionCallManager();
            functionManager.registerTool(DirectoryListTool.createTool());
            
            // 创建OpenAI客户端并设置Function Call管理器
            OpenaiClient openaiClient = new OpenaiClient();
            openaiClient.setFunctionCallManager(functionManager);
            
            // 消息历史
            List<com.example.chat.model.OpenAIRequest.Message> messageHistory = new ArrayList<>();
            
            // 注册消息发送监听器
            chatAssistant.onMessageSent(userMessage -> {
                System.out.println("用户发送了消息: " + userMessage);
                handleUserMessage(chatAssistant, openaiClient, messageHistory, userMessage);
            });
            
            // 注册消息编辑监听器
            chatAssistant.onMessageEdited((messageIndex, newContent) -> {
                System.out.println("用户编辑了消息 [索引: " + messageIndex + "]: " + newContent);
                // 在实际应用中，这里需要更新messageHistory并可能重新生成响应
            });
            
            // 注册删除消息监听器
            chatAssistant.onDeleteMessages(messageIndices -> {
                System.out.println("用户删除了消息，索引: " + messageIndices);
                // 在实际应用中，这里需要从messageHistory中删除对应的消息
            });
            
            // 设置初始消息
            setInitialMessages(chatAssistant, messageHistory);
            
            // 显示界面
            chatAssistant.setVisible(true);
            
            System.out.println("=== Function Call 集成测试启动 ===");
            System.out.println("已注册工具:");
            for (Tool tool : functionManager.getRegisteredTools()) {
                System.out.println("  - " + tool.getName() + ": " + tool.getDescription());
            }
            System.out.println("尝试发送消息: '请列出当前目录的文件'");
            System.out.println("================================");
        });
    }
    
    /**
     * 设置初始消息
     */
    private static void setInitialMessages(CopilotChatAssistant chatAssistant, 
                                         List<com.example.chat.model.OpenAIRequest.Message> messageHistory) {
        List<com.example.chat.model.OpenAIRequest.Message> messages = new ArrayList<>();
        
        // 系统消息
        com.example.chat.model.OpenAIRequest.Message systemMessage = 
            new com.example.chat.model.OpenAIRequest.Message("system", 
                "You are a helpful AI assistant with access to file system tools. " +
                "You can list directories and help users navigate their file system. " +
                "When users ask about files or directories, use the available tools to provide accurate information.");
        messages.add(systemMessage);
        messageHistory.add(systemMessage);
        
        // 欢迎消息
        com.example.chat.model.OpenAIRequest.Message welcomeMessage = 
            new com.example.chat.model.OpenAIRequest.Message("assistant", 
                "Hi! I'm your AI assistant with file system capabilities. 🤖\n\n" +
                "I can help you with:\n" +
                "• 📁 List directory contents\n" +
                "• 🔍 Navigate file systems\n" +
                "• 📊 Show file details (size, permissions, etc.)\n\n" +
                "Try asking me to:\n" +
                "- \"List the current directory\"\n" +
                "- \"Show me the files in src folder\"\n" +
                "- \"What files are in the current directory with details?\"\n\n" +
                "I'll use my tools to give you accurate, real-time information!");
        messages.add(welcomeMessage);
        messageHistory.add(welcomeMessage);
        
        chatAssistant.setMessages(messages);
    }
    
    /**
     * 处理用户消息
     */
    private static void handleUserMessage(CopilotChatAssistant chatAssistant, 
                                        OpenaiClient openaiClient,
                                        List<com.example.chat.model.OpenAIRequest.Message> messageHistory,
                                        String userMessage) {
        // 设置AI正在回复状态
        chatAssistant.setReplyingStatus(true);
        
        // 在后台线程中处理，避免阻塞UI
        new Thread(() -> {
            try {
                System.out.println("正在处理用户消息: " + userMessage);
                
                // 调用OpenAI API（支持Function Call）
                List<com.example.chat.model.OpenAIRequest.Message> updatedMessages = 
                    openaiClient.generateOpenaiResponse(messageHistory, userMessage);
                
                // 更新消息历史
                messageHistory.clear();
                messageHistory.addAll(updatedMessages);
                
                // 在UI线程中更新界面
                SwingUtilities.invokeLater(() -> {
                    chatAssistant.setMessages(updatedMessages);
                    chatAssistant.setReplyingStatus(false);
                });
                
                System.out.println("消息处理完成，共 " + updatedMessages.size() + " 条消息");
                
            } catch (Exception e) {
                System.err.println("处理消息时发生错误: " + e.getMessage());
                e.printStackTrace();
                
                // 显示错误消息
                SwingUtilities.invokeLater(() -> {
                    List<com.example.chat.model.OpenAIRequest.Message> errorMessages = new ArrayList<>(messageHistory);
                    errorMessages.add(new com.example.chat.model.OpenAIRequest.Message("user", userMessage));
                    errorMessages.add(new com.example.chat.model.OpenAIRequest.Message("assistant", 
                        "抱歉，处理您的请求时发生了错误: " + e.getMessage()));
                    
                    chatAssistant.setMessages(errorMessages);
                    chatAssistant.setReplyingStatus(false);
                });
            }
        }).start();
    }
}
