package com.example.chat.model;

import java.util.List;

/**
 * 简化的消息类，用于测试，不依赖Jackson
 */
public class TestMessage {
    private String role;
    private String content;
    private String name;
    private String toolCallId;
    private List<Object> toolCalls; // 简化为Object类型
    
    public TestMessage() {}
    
    public TestMessage(String role, String content) {
        this.role = role;
        this.content = content;
    }
    
    public TestMessage(String role, String content, String name) {
        this.role = role;
        this.content = content;
        this.name = name;
    }
    
    // Getters and Setters
    public String getRole() { return role; }
    public void setRole(String role) { this.role = role; }
    
    public String getContent() { return content; }
    public void setContent(String content) { this.content = content; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getToolCallId() { return toolCallId; }
    public void setToolCallId(String toolCallId) { this.toolCallId = toolCallId; }
    
    public List<Object> getToolCalls() { return toolCalls; }
    public void setToolCalls(List<Object> toolCalls) { this.toolCalls = toolCalls; }
    
    @Override
    public String toString() {
        return "TestMessage{role='" + role + "', content='" + content +
               "', name='" + name + "', toolCallId='" + toolCallId + "'}";
    }
}
