package com.example.chat;

import javax.swing.SwingUtilities;
import javax.swing.Timer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 测试清理会话和删除消息回调功能
 */
public class CallbackTest {
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // 创建聊天助手实例
            CopilotChatAssistant chatAssistant = new CopilotChatAssistant();
            
            // 注册消息发送监听器
            chatAssistant.onMessageSent(userMessage -> {
                System.out.println("用户发送了消息: " + userMessage);
                handleUserMessage(chatAssistant, userMessage);
            });
            
            // 注册清理会话监听器
            chatAssistant.onClearSession(() -> {
                System.out.println("用户请求清理会话");
                
                // 这里可以调用你的API来清理会话
                // 然后更新UI
                List<ChatInterface.OpenAIMessage> emptyMessages = new ArrayList<>();
                chatAssistant.setMessages(emptyMessages);
                
                System.out.println("会话已清理");
            });
            
            // 注册删除消息监听器
            chatAssistant.onDeleteMessages(messageIndices -> {
                System.out.println("用户请求删除消息，索引: " + messageIndices);
                
                // 这里应该从你的消息数据源中删除对应的消息
                // 然后重新设置消息列表
                // 为了演示，我们只是打印信息
                
                // 模拟删除操作后的结果
                Timer timer = new Timer(500, e -> {
                    System.out.println("消息删除完成，刷新界面");
                    // 实际应用中，你会从数据源获取更新后的消息列表
                    // chatAssistant.setMessages(updatedMessages);
                });
                timer.setRepeats(false);
                timer.start();
            });
            
            // 设置初始消息
            setInitialMessages(chatAssistant);
            
            // 显示界面
            chatAssistant.setVisible(true);
            
            System.out.println("=== 使用说明 ===");
            System.out.println("1. 点击标题栏的垃圾桶图标可以清理整个会话");
            System.out.println("2. 鼠标悬停到消息气泡上时，右下角会显示复制📋和删除🗑按钮");
            System.out.println("3. 右键点击消息气泡可以选择'Delete from here'删除从该消息开始的所有消息");
            System.out.println("4. 查看控制台输出了解回调事件");
        });
    }
    
    /**
     * 设置初始消息
     */
    private static void setInitialMessages(ChatInterface chatInterface) {
        List<ChatInterface.OpenAIMessage> messages = new ArrayList<>();
        
        messages.add(new ChatInterface.OpenAIMessage("system", 
            "You are a helpful assistant."));
        
        messages.add(new ChatInterface.OpenAIMessage("user", 
            "Hello! Can you help me with something?"));
        
        messages.add(new ChatInterface.OpenAIMessage("assistant", 
            "Hello! I'd be happy to help you. What do you need assistance with?"));
        
        messages.add(new ChatInterface.OpenAIMessage("user", 
            "What's the weather like today?"));
        
        // 创建带工具调用的消息
        ChatInterface.OpenAIMessage assistantWithTools = new ChatInterface.OpenAIMessage("assistant", 
            "I'll check the weather for you.");
        
        List<ChatInterface.ToolCall> toolCalls = Arrays.asList(
            new ChatInterface.ToolCall("call_1", "function", 
                new ChatInterface.FunctionCall("get_weather", 
                    "{\"location\": \"current\"}"))
        );
        assistantWithTools.setToolCalls(toolCalls);
        messages.add(assistantWithTools);
        
        // 工具结果
        ChatInterface.OpenAIMessage toolResult = new ChatInterface.OpenAIMessage("tool", 
            "Current weather: 22°C, partly cloudy");
        toolResult.setToolCallId("call_1");
        toolResult.setName("get_weather");
        messages.add(toolResult);
        
        // 最终回答
        messages.add(new ChatInterface.OpenAIMessage("assistant", 
            "Based on the weather data, it's currently 22°C with partly cloudy skies. It's a nice day!"));
        
        chatInterface.setMessages(messages);
    }
    
    /**
     * 处理用户消息
     */
    private static void handleUserMessage(ChatInterface chatInterface, String userMessage) {
        chatInterface.setReplyingStatus(true);
        
        Timer timer = new Timer(1500, e -> {
            // 模拟AI回复
            List<ChatInterface.OpenAIMessage> updatedMessages = new ArrayList<>();
            
            // 这里应该是从你的数据源获取所有历史消息
            // 为了演示，我们创建一个简单的对话
            updatedMessages.add(new ChatInterface.OpenAIMessage("user", userMessage));
            updatedMessages.add(new ChatInterface.OpenAIMessage("assistant", 
                "I received your message: \"" + userMessage + "\"\n\n" +
                "You can try:\n" +
                "• Right-click on any message to see context menu\n" +
                "• Click the trash icon in header to clear session\n" +
                "• Check the console for callback events"));
            
            chatInterface.setMessages(updatedMessages);
            chatInterface.setReplyingStatus(false);
        });
        timer.setRepeats(false);
        timer.start();
    }
}