package com.example.chat.service;

import java.util.List;
import java.util.Map;

/**
 * 工具定义类
 * 包含工具的元数据和执行逻辑
 */
public class Tool {
    private final String name;
    private final String description;
    private final List<ToolParameter> parameters;
    private final ToolFunction function;
    
    public Tool(String name, String description, List<ToolParameter> parameters, ToolFunction function) {
        this.name = name;
        this.description = description;
        this.parameters = parameters;
        this.function = function;
    }
    
    public String getName() { return name; }
    public String getDescription() { return description; }
    public List<ToolParameter> getParameters() { return parameters; }
    public ToolFunction getFunction() { return function; }
    
    /**
     * 工具参数定义
     */
    public static class ToolParameter {
        private final String name;
        private final String type;
        private final String description;
        private final boolean required;
        private final Object defaultValue;
        private final List<String> enumValues;
        
        public ToolParameter(String name, String type, String description, boolean required) {
            this(name, type, description, required, null, null);
        }
        
        public ToolParameter(String name, String type, String description, boolean required, Object defaultValue) {
            this(name, type, description, required, defaultValue, null);
        }
        
        public ToolParameter(String name, String type, String description, boolean required, Object defaultValue, List<String> enumValues) {
            this.name = name;
            this.type = type;
            this.description = description;
            this.required = required;
            this.defaultValue = defaultValue;
            this.enumValues = enumValues;
        }
        
        public String getName() { return name; }
        public String getType() { return type; }
        public String getDescription() { return description; }
        public boolean isRequired() { return required; }
        public Object getDefaultValue() { return defaultValue; }
        public List<String> getEnumValues() { return enumValues; }
    }
    
    /**
     * 工具执行函数接口
     */
    @FunctionalInterface
    public interface ToolFunction {
        /**
         * 执行工具逻辑
         * @param arguments 工具参数
         * @return 执行结果
         * @throws Exception 执行异常
         */
        ToolExecutionResult execute(Map<String, Object> arguments) throws Exception;
    }
    
    @Override
    public String toString() {
        return "Tool{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", parameters=" + parameters.size() +
                '}';
    }
}
