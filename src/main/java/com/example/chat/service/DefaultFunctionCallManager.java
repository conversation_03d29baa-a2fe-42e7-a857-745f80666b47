package com.example.chat.service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Function Call 管理器的默认实现
 */
public class DefaultFunctionCallManager implements FunctionCallManager {
    
    private final Map<String, Tool> registeredTools;

    public DefaultFunctionCallManager() {
        this.registeredTools = new ConcurrentHashMap<>();
    }
    
    @Override
    public void registerTool(Tool tool) {
        if (tool == null) {
            throw new IllegalArgumentException("工具不能为null");
        }
        if (tool.getName() == null || tool.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("工具名称不能为空");
        }
        
        registeredTools.put(tool.getName(), tool);
        System.out.println("已注册工具: " + tool.getName() + " - " + tool.getDescription());
    }
    
    @Override
    public void registerTools(List<Tool> tools) {
        if (tools != null) {
            for (Tool tool : tools) {
                registerTool(tool);
            }
        }
    }
    
    @Override
    public List<Tool> getRegisteredTools() {
        return new ArrayList<>(registeredTools.values());
    }
    
    @Override
    public Tool getTool(String toolName) {
        return registeredTools.get(toolName);
    }
    
    @Override
    public boolean isToolRegistered(String toolName) {
        return registeredTools.containsKey(toolName);
    }
    
    @Override
    public ToolExecutionResult callTool(String toolName, String arguments) throws ToolExecutionException {
        try {
            // 简单的JSON解析（仅支持基本格式）
            Map<String, Object> argumentsMap = parseSimpleJson(arguments);
            return callTool(toolName, argumentsMap);
        } catch (Exception e) {
            throw new ToolExecutionException(toolName, "ARGUMENT_PARSE_ERROR",
                "无法解析工具参数: " + e.getMessage(), e);
        }
    }
    
    @Override
    public ToolExecutionResult callTool(String toolName, Map<String, Object> arguments) throws ToolExecutionException {
        Tool tool = registeredTools.get(toolName);
        if (tool == null) {
            throw new ToolExecutionException(toolName, "TOOL_NOT_FOUND", "工具未找到: " + toolName);
        }
        
        try {
            // 验证参数
            validateArguments(tool, arguments);
            
            // 记录开始时间
            long startTime = System.currentTimeMillis();
            
            // 执行工具
            ToolExecutionResult result = tool.getFunction().execute(arguments);
            
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 如果结果没有包含执行时间，创建一个新的结果对象
            if (result.getExecutionDurationMs() == 0 && executionTime > 0) {
                return new ToolExecutionResult(result.getResult(), executionTime, result.getMetadata());
            }
            
            return result;
            
        } catch (ToolExecutionException e) {
            throw e;
        } catch (Exception e) {
            throw new ToolExecutionException(toolName, "EXECUTION_ERROR", 
                "工具执行失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public List<Map<String, Object>> getOpenAIFunctionDefinitions() {
        List<Map<String, Object>> definitions = new ArrayList<>();
        
        for (Tool tool : registeredTools.values()) {
            Map<String, Object> definition = new HashMap<>();
            definition.put("type", "function");
            
            Map<String, Object> function = new HashMap<>();
            function.put("name", tool.getName());
            function.put("description", tool.getDescription());
            
            // 构建参数定义
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("type", "object");
            
            Map<String, Object> properties = new HashMap<>();
            List<String> required = new ArrayList<>();
            
            for (Tool.ToolParameter param : tool.getParameters()) {
                Map<String, Object> paramDef = new HashMap<>();
                paramDef.put("type", param.getType());
                paramDef.put("description", param.getDescription());
                
                if (param.getEnumValues() != null && !param.getEnumValues().isEmpty()) {
                    paramDef.put("enum", param.getEnumValues());
                }
                
                properties.put(param.getName(), paramDef);
                
                if (param.isRequired()) {
                    required.add(param.getName());
                }
            }
            
            parameters.put("properties", properties);
            if (!required.isEmpty()) {
                parameters.put("required", required);
            }
            
            function.put("parameters", parameters);
            definition.put("function", function);
            
            definitions.add(definition);
        }
        
        return definitions;
    }
    
    @Override
    public boolean unregisterTool(String toolName) {
        Tool removed = registeredTools.remove(toolName);
        if (removed != null) {
            System.out.println("已注销工具: " + toolName);
            return true;
        }
        return false;
    }
    
    @Override
    public void clearAllTools() {
        int count = registeredTools.size();
        registeredTools.clear();
        System.out.println("已清空所有工具，共 " + count + " 个");
    }
    
    /**
     * 验证工具参数
     */
    private void validateArguments(Tool tool, Map<String, Object> arguments) throws ToolExecutionException {
        for (Tool.ToolParameter param : tool.getParameters()) {
            String paramName = param.getName();
            Object value = arguments.get(paramName);
            
            // 检查必需参数
            if (param.isRequired() && (value == null || (value instanceof String && ((String) value).trim().isEmpty()))) {
                throw new ToolExecutionException(tool.getName(), "MISSING_REQUIRED_PARAMETER", 
                    "缺少必需参数: " + paramName);
            }
            
            // 如果参数有默认值且当前值为空，设置默认值
            if (value == null && param.getDefaultValue() != null) {
                arguments.put(paramName, param.getDefaultValue());
            }
        }
    }

    /**
     * 简单的JSON解析（仅支持基本格式）
     */
    private Map<String, Object> parseSimpleJson(String json) {
        Map<String, Object> result = new HashMap<>();

        if (json == null || json.trim().isEmpty()) {
            return result;
        }

        // 移除大括号和空白
        json = json.trim();
        if (json.startsWith("{")) json = json.substring(1);
        if (json.endsWith("}")) json = json.substring(0, json.length() - 1);

        // 简单解析键值对
        String[] pairs = json.split(",");
        for (String pair : pairs) {
            String[] keyValue = pair.split(":");
            if (keyValue.length == 2) {
                String key = keyValue[0].trim().replaceAll("\"", "");
                String value = keyValue[1].trim().replaceAll("\"", "");

                // 尝试转换类型
                Object parsedValue = parseValue(value);
                result.put(key, parsedValue);
            }
        }

        return result;
    }

    /**
     * 解析值类型
     */
    private Object parseValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return "";
        }

        value = value.trim();

        // 布尔值
        if ("true".equalsIgnoreCase(value)) return true;
        if ("false".equalsIgnoreCase(value)) return false;

        // 数字
        try {
            if (value.contains(".")) {
                return Double.parseDouble(value);
            } else {
                return Integer.parseInt(value);
            }
        } catch (NumberFormatException e) {
            // 不是数字，返回字符串
        }

        return value;
    }
}
