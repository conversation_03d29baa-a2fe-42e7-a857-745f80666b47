package com.example.chat.service;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 工具执行结果类
 */
public class ToolExecutionResult {
    private final boolean success;
    private final String result;
    private final String errorMessage;
    private final String errorCode;
    private final LocalDateTime executionTime;
    private final long executionDurationMs;
    private final Map<String, Object> metadata;
    
    // 成功结果构造器
    public ToolExecutionResult(String result) {
        this(true, result, null, null, LocalDateTime.now(), 0, null);
    }
    
    public ToolExecutionResult(String result, long executionDurationMs) {
        this(true, result, null, null, LocalDateTime.now(), executionDurationMs, null);
    }
    
    public ToolExecutionResult(String result, long executionDurationMs, Map<String, Object> metadata) {
        this(true, result, null, null, LocalDateTime.now(), executionDurationMs, metadata);
    }
    
    // 失败结果构造器
    public static ToolExecutionResult failure(String errorMessage) {
        return new ToolExecutionResult(false, null, errorMessage, "EXECUTION_ERROR", LocalDateTime.now(), 0, null);
    }
    
    public static ToolExecutionResult failure(String errorCode, String errorMessage) {
        return new ToolExecutionResult(false, null, errorMessage, errorCode, LocalDateTime.now(), 0, null);
    }
    
    public static ToolExecutionResult failure(String errorCode, String errorMessage, long executionDurationMs) {
        return new ToolExecutionResult(false, null, errorMessage, errorCode, LocalDateTime.now(), executionDurationMs, null);
    }
    
    // 完整构造器
    private ToolExecutionResult(boolean success, String result, String errorMessage, String errorCode, 
                               LocalDateTime executionTime, long executionDurationMs, Map<String, Object> metadata) {
        this.success = success;
        this.result = result;
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
        this.executionTime = executionTime;
        this.executionDurationMs = executionDurationMs;
        this.metadata = metadata;
    }
    
    // Getters
    public boolean isSuccess() { return success; }
    public String getResult() { return result; }
    public String getErrorMessage() { return errorMessage; }
    public String getErrorCode() { return errorCode; }
    public LocalDateTime getExecutionTime() { return executionTime; }
    public long getExecutionDurationMs() { return executionDurationMs; }
    public Map<String, Object> getMetadata() { return metadata; }
    
    /**
     * 获取用于OpenAI API的结果字符串
     * @return 格式化的结果字符串
     */
    public String getFormattedResult() {
        if (success) {
            return result != null ? result : "执行成功";
        } else {
            return "错误: " + (errorMessage != null ? errorMessage : "未知错误");
        }
    }
    
    /**
     * 获取详细的执行信息
     * @return 包含执行时间、耗时等信息的字符串
     */
    public String getDetailedInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("执行时间: ").append(executionTime).append("\n");
        sb.append("执行耗时: ").append(executionDurationMs).append("ms\n");
        sb.append("执行状态: ").append(success ? "成功" : "失败").append("\n");
        
        if (success) {
            sb.append("执行结果: ").append(result != null ? result : "无返回值");
        } else {
            sb.append("错误代码: ").append(errorCode != null ? errorCode : "UNKNOWN").append("\n");
            sb.append("错误信息: ").append(errorMessage != null ? errorMessage : "未知错误");
        }
        
        if (metadata != null && !metadata.isEmpty()) {
            sb.append("\n元数据: ").append(metadata);
        }
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return "ToolExecutionResult{" +
                "success=" + success +
                ", result='" + result + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", errorCode='" + errorCode + '\'' +
                ", executionTime=" + executionTime +
                ", executionDurationMs=" + executionDurationMs +
                '}';
    }
}
