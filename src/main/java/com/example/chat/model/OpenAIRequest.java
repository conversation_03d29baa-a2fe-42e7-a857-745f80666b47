package com.example.chat.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * OpenAI Chat Completions API 请求模型
 */
public class OpenAIRequest {
    
    @JsonProperty("model")
    private String model;
    
    @JsonProperty("messages")
    private List<Message> messages;
    
    @JsonProperty("temperature")
    private Double temperature;
    
    @JsonProperty("max_tokens")
    private Integer maxTokens;
    
    @JsonProperty("stream")
    private Boolean stream;

    @JsonProperty("tools")
    private List<Object> tools;

    @JsonProperty("tool_choice")
    private Object toolChoice;

    // 构造函数
    public OpenAIRequest() {}
    
    public OpenAIRequest(String model, List<Message> messages) {
        this.model = model;
        this.messages = messages;
        this.stream = false;
    }
    
    // Getters and Setters
    public String getModel() { return model; }
    public void setModel(String model) { this.model = model; }
    
    public List<Message> getMessages() { return messages; }
    public void setMessages(List<Message> messages) { this.messages = messages; }
    
    public Double getTemperature() { return temperature; }
    public void setTemperature(Double temperature) { this.temperature = temperature; }
    
    public Integer getMaxTokens() { return maxTokens; }
    public void setMaxTokens(Integer maxTokens) { this.maxTokens = maxTokens; }
    
    public Boolean getStream() { return stream; }
    public void setStream(Boolean stream) { this.stream = stream; }

    public List<Object> getTools() { return tools; }
    public void setTools(List<Object> tools) { this.tools = tools; }

    public Object getToolChoice() { return toolChoice; }
    public void setToolChoice(Object toolChoice) { this.toolChoice = toolChoice; }
    
    /**
     * 统一的消息类 - 同时支持 UI 层和 API 层使用
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Message {
        // OpenAI API 标准字段
        @JsonProperty("role")
        private String role;          // "user", "assistant", "system", "function", "tool"

        @JsonProperty("content")
        private String content;       // 消息内容

        @JsonProperty("name")
        private String name;          // function 消息的函数名称

        @JsonProperty("tool_call_id")
        private String toolCallId;    // tool call ID

        @JsonProperty("tool_calls")
        private List<ToolCall> toolCalls;  // assistant 发起的工具调用

        // 构造函数
        public Message() {}

        public Message(String role, String content) {
            this.role = role;
            this.content = content;
        }

        public Message(String role, String content, String name) {
            this.role = role;
            this.content = content;
            this.name = name;
        }

        // Getters and Setters
        public String getRole() { return role; }
        public void setRole(String role) { this.role = role; }

        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getToolCallId() { return toolCallId; }
        public void setToolCallId(String toolCallId) { this.toolCallId = toolCallId; }

        public List<ToolCall> getToolCalls() { return toolCalls; }
        public void setToolCalls(List<ToolCall> toolCalls) { this.toolCalls = toolCalls; }

        @Override
        public String toString() {
            return "Message{role='" + role + "', content='" + content +
                   "', name='" + name + "', toolCallId='" + toolCallId + "'}";
        }
    }

    /**
     * 工具调用类
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ToolCall {
        @JsonProperty("id")
        private String id;

        @JsonProperty("type")
        private String type;

        @JsonProperty("function")
        private FunctionCall function;

        public ToolCall() {}

        public ToolCall(String id, String type, FunctionCall function) {
            this.id = id;
            this.type = type;
            this.function = function;
        }

        public String getId() { return id; }
        public void setId(String id) { this.id = id; }

        public String getType() { return type; }
        public void setType(String type) { this.type = type; }

        public FunctionCall getFunction() { return function; }
        public void setFunction(FunctionCall function) { this.function = function; }
    }

    /**
     * 函数调用类
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FunctionCall {
        @JsonProperty("name")
        private String name;

        @JsonProperty("arguments")
        private String arguments;

        public FunctionCall() {}

        public FunctionCall(String name, String arguments) {
            this.name = name;
            this.arguments = arguments;
        }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getArguments() { return arguments; }
        public void setArguments(String arguments) { this.arguments = arguments; }
    }
}
