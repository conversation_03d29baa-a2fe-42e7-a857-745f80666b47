package com.example.chat;

import com.example.chat.model.OpenAIRequest;
import java.util.List;

/**
 * 聊天助手对外接口
 */
public interface ChatInterface {
    
    /**
     * 设置消息列表并刷新界面
     * @param messages OpenAI API格式的消息数组
     */
    void setMessages(List<OpenAIRequest.Message> messages);
    
    /**
     * 注册消息发送事件监听器
     * @param listener 当用户发送消息时的回调
     */
    void onMessageSent(MessageSentListener listener);
    
    /**
     * 设置AI回复状态
     * @param isReplying true表示AI正在回复中，false表示回复完成
     */
    void setReplyingStatus(boolean isReplying);
    
    /**
     * 注册清理会话事件监听器
     * @param listener 当用户手动清理会话时的回调
     */
    void onClearSession(ClearSessionListener listener);
    
    /**
     * 注册删除消息事件监听器
     * @param listener 当用户删除指定消息时的回调
     */
    void onDeleteMessages(DeleteMessagesListener listener);

    /**
     * 注册消息编辑事件监听器
     * @param listener 当用户编辑指定消息时的回调
     */
    void onMessageEdited(MessageEditedListener listener);

    /**
     * 消息发送事件监听器
     */
    @FunctionalInterface
    interface MessageSentListener {
        /**
         * 当用户发送消息时触发
         * @param content 用户发送的消息内容
         */
        void onMessageSent(String content);
    }
    
    /**
     * 清理会话事件监听器
     */
    @FunctionalInterface
    interface ClearSessionListener {
        /**
         * 当用户手动清理会话时触发
         */
        void onClearSession();
    }
    
    /**
     * 删除消息事件监听器
     */
    @FunctionalInterface
    interface DeleteMessagesListener {
        /**
         * 当用户删除指定消息时触发
         * @param messageIndices 要删除的消息索引列表（基于当前显示的消息列表）
         */
        void onDeleteMessages(List<Integer> messageIndices);
    }

    /**
     * 消息编辑事件监听器
     */
    @FunctionalInterface
    interface MessageEditedListener {
        /**
         * 当用户编辑指定消息时触发
         * @param messageIndex 被编辑的消息索引（基于当前显示的消息列表）
         * @param newContent 编辑后的新内容
         */
        void onMessageEdited(int messageIndex, String newContent);
    }
    
    // 类型别名，便于代码阅读和向后兼容
    // 统一使用 OpenAIRequest 中的定义，避免重复

    /**
     * 消息类型别名 - 实际为 OpenAIRequest.Message
     * 这样既保持了 API 的一致性，又避免了重复定义
     */
    class OpenAIMessage extends OpenAIRequest.Message {
        public OpenAIMessage() { super(); }
        public OpenAIMessage(String role, String content) { super(role, content); }
        public OpenAIMessage(String role, String content, String name) { super(role, content, name); }
    }

    /**
     * 工具调用类型别名 - 实际为 OpenAIRequest.ToolCall
     */
    class ToolCall extends OpenAIRequest.ToolCall {
        public ToolCall() { super(); }
        public ToolCall(String id, String type, OpenAIRequest.FunctionCall function) { super(id, type, function); }
    }

    /**
     * 函数调用类型别名 - 实际为 OpenAIRequest.FunctionCall
     */
    class FunctionCall extends OpenAIRequest.FunctionCall {
        public FunctionCall() { super(); }
        public FunctionCall(String name, String arguments) { super(name, arguments); }
    }
}