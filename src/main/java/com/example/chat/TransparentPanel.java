package com.example.chat;

import javax.swing.*;
import java.awt.*;

/**
 * 一个可以将其自身及其所有子组件一起设置为半透明的JPanel。
 */
public class TransparentPanel extends JPanel {

    private float alpha = 1.0f; // 默认为完全不透明

    public TransparentPanel() {
        super();
    }
    public TransparentPanel(FlowLayout flowLayout) {
        super(flowLayout);
        // 必须设置为false，否则该面板会自己绘制一个不透明的背景
        setOpaque(false);
    }

    /**
     * 设置面板的透明度。
     * @param alpha 透明度值，从 0.0f (完全透明) 到 1.0f (完全不透明)。
     */
    public void setAlpha(float alpha) {
        if (alpha < 0.0f || alpha > 1.0f) {
            throw new IllegalArgumentException("Alpha value must be between 0.0 and 1.0");
        }
        this.alpha = alpha;
        // 请求重绘以应用新的透明度
        repaint();
    }

    public float getAlpha() {
        return alpha;
    }

    /**
     * 重写paint方法，以在绘制自身和子组件之前应用AlphaComposite。
     */
    @Override
    public void paint(Graphics g) {
        Graphics2D g2d = (Graphics2D) g.create();

        // 保存原始的Composite
        Composite oldComposite = g2d.getComposite();
        try {
            // 设置新的、带有透明度的Composite
            // AlphaComposite.SRC_OVER 是最常用的混合规则
            g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha));

            // 调用父类的paint方法。这将以新的透明度规则来绘制
            // 面板自身(paintComponent)、其边框(paintBorder)和所有子组件(paintChildren)。
            super.paint(g2d);

        } finally {
            // 关键步骤：无论如何都要恢复原始的Composite，以免影响其他组件的绘制
            g2d.setComposite(oldComposite);
            g2d.dispose();
        }
    }
}