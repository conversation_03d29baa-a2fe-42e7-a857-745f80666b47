package com.example.chat;

import javax.swing.*;
import javax.swing.event.ChangeEvent;
import javax.swing.event.ChangeListener;
import java.awt.*;
import java.net.URL;

public class TrueTransparencyExample {

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            JFrame frame = new JFrame("真正透明的JPanel示例");
            frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            frame.setSize(500, 400);

            // --- 1. 设置一个背景，以便观察透明效果 ---
            try {
                // 使用一张网络图片作为背景
                URL imageUrl = new URL("https://i.imgur.com/5GARGq6.jpeg");
                JLabel background = new JLabel(new ImageIcon(imageUrl));
                frame.setContentPane(background);
            } catch (Exception e) {
                // 如果图片加载失败，就用颜色背景
                frame.getContentPane().setBackground(Color.DARK_GRAY);
                System.err.println("背景图片加载失败: " + e.getMessage());
            }
            frame.setLayout(new GridBagLayout()); // 使用布局管理器来居中面板

            // --- 2. 创建并使用我们的 TransparentPanel ---
            TransparentPanel transparentPanel = new TransparentPanel();
            transparentPanel.setLayout(new FlowLayout(FlowLayout.CENTER, 20, 20));
            transparentPanel.setBorder(BorderFactory.createTitledBorder("这是一个整体透明的面板"));

            // 在面板中添加一些普通、不透明的组件
            transparentPanel.add(new JLabel("标签:"));
            transparentPanel.add(new JTextField("文本框"));
            transparentPanel.add(new JButton("按钮"));
            transparentPanel.add(new JCheckBox("复选框"));

            // --- 3. 创建一个滑块来动态控制透明度 ---
            JSlider alphaSlider = new JSlider(0, 100, 75); // 初始透明度为 0.75
            transparentPanel.setAlpha(alphaSlider.getValue() / 100f); // 设置初始值

            alphaSlider.addChangeListener(new ChangeListener() {
                @Override
                public void stateChanged(ChangeEvent e) {
                    float newAlpha = alphaSlider.getValue() / 100f;
                    transparentPanel.setAlpha(newAlpha);
                    System.out.println(newAlpha);
                }
            });

            // 将面板和滑块添加到窗口中
            GridBagConstraints gbc = new GridBagConstraints();
            gbc.gridy = 0;
            gbc.insets = new Insets(10, 10, 10, 10);
            frame.add(transparentPanel, gbc);

            gbc.gridy = 1;
            frame.add(alphaSlider, gbc);

            frame.setLocationRelativeTo(null);
            frame.setVisible(true);
        });
    }
}