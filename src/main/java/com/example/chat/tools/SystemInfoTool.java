package com.example.chat.tools;

import com.example.chat.service.Tool;
import com.example.chat.service.ToolExecutionResult;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.RuntimeMXBean;
import java.util.*;

/**
 * 系统信息工具
 */
public class SystemInfoTool {
    
    /**
     * 创建系统信息工具
     */
    public static Tool createTool() {
        List<Tool.ToolParameter> parameters = Arrays.asList(
            new Tool.ToolParameter("info_type", "string", "要获取的信息类型", false, "all", 
                Arrays.asList("all", "os", "memory", "runtime", "properties"))
        );
        
        return new Tool("get_system_info", "获取系统信息（操作系统、内存、运行时等）", parameters, SystemInfoTool::execute);
    }
    
    /**
     * 执行系统信息获取
     */
    private static ToolExecutionResult execute(Map<String, Object> arguments) throws Exception {
        long startTime = System.currentTimeMillis();
        
        try {
            String infoType = (String) arguments.getOrDefault("info_type", "all");
            
            StringBuilder result = new StringBuilder();
            
            switch (infoType.toLowerCase()) {
                case "os":
                    result.append(getOperatingSystemInfo());
                    break;
                case "memory":
                    result.append(getMemoryInfo());
                    break;
                case "runtime":
                    result.append(getRuntimeInfo());
                    break;
                case "properties":
                    result.append(getSystemProperties());
                    break;
                case "all":
                default:
                    result.append("=== 系统信息概览 ===\n\n");
                    result.append(getOperatingSystemInfo()).append("\n");
                    result.append(getMemoryInfo()).append("\n");
                    result.append(getRuntimeInfo()).append("\n");
                    break;
            }
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("info_type", infoType);
            metadata.put("timestamp", new Date());
            
            return new ToolExecutionResult(result.toString(), executionTime, metadata);
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            return ToolExecutionResult.failure("EXECUTION_ERROR", 
                "获取系统信息时发生错误: " + e.getMessage(), executionTime);
        }
    }
    
    /**
     * 获取操作系统信息
     */
    private static String getOperatingSystemInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("🖥️ 操作系统信息:\n");
        
        OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
        
        sb.append("  操作系统: ").append(osBean.getName()).append("\n");
        sb.append("  版本: ").append(osBean.getVersion()).append("\n");
        sb.append("  架构: ").append(osBean.getArch()).append("\n");
        sb.append("  可用处理器: ").append(osBean.getAvailableProcessors()).append(" 核\n");
        sb.append("  系统负载: ").append(String.format("%.2f", osBean.getSystemLoadAverage())).append("\n");
        
        return sb.toString();
    }
    
    /**
     * 获取内存信息
     */
    private static String getMemoryInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("💾 内存信息:\n");
        
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        Runtime runtime = Runtime.getRuntime();
        
        // JVM内存信息
        long heapUsed = memoryBean.getHeapMemoryUsage().getUsed();
        long heapMax = memoryBean.getHeapMemoryUsage().getMax();
        long nonHeapUsed = memoryBean.getNonHeapMemoryUsage().getUsed();
        
        sb.append("  JVM堆内存: ").append(formatBytes(heapUsed)).append(" / ").append(formatBytes(heapMax)).append("\n");
        sb.append("  JVM非堆内存: ").append(formatBytes(nonHeapUsed)).append("\n");
        
        // Runtime内存信息
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        long usedMemory = totalMemory - freeMemory;
        
        sb.append("  已分配内存: ").append(formatBytes(totalMemory)).append("\n");
        sb.append("  已使用内存: ").append(formatBytes(usedMemory)).append("\n");
        sb.append("  空闲内存: ").append(formatBytes(freeMemory)).append("\n");
        sb.append("  最大可用内存: ").append(formatBytes(maxMemory)).append("\n");
        
        return sb.toString();
    }
    
    /**
     * 获取运行时信息
     */
    private static String getRuntimeInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("⚙️ 运行时信息:\n");
        
        RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
        
        sb.append("  JVM名称: ").append(runtimeBean.getVmName()).append("\n");
        sb.append("  JVM版本: ").append(runtimeBean.getVmVersion()).append("\n");
        sb.append("  JVM供应商: ").append(runtimeBean.getVmVendor()).append("\n");
        sb.append("  Java版本: ").append(System.getProperty("java.version")).append("\n");
        sb.append("  启动时间: ").append(new Date(runtimeBean.getStartTime())).append("\n");
        sb.append("  运行时长: ").append(formatDuration(runtimeBean.getUptime())).append("\n");
        
        return sb.toString();
    }
    
    /**
     * 获取系统属性
     */
    private static String getSystemProperties() {
        StringBuilder sb = new StringBuilder();
        sb.append("🔧 系统属性:\n");
        
        Properties props = System.getProperties();
        String[] importantProps = {
            "java.home", "java.class.path", "user.name", "user.home", 
            "user.dir", "file.separator", "path.separator", "line.separator"
        };
        
        for (String prop : importantProps) {
            String value = props.getProperty(prop);
            if (value != null) {
                if (prop.equals("java.class.path")) {
                    sb.append("  ").append(prop).append(": ").append(value.length() > 100 ? 
                        value.substring(0, 100) + "..." : value).append("\n");
                } else {
                    sb.append("  ").append(prop).append(": ").append(value).append("\n");
                }
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 格式化字节数
     */
    private static String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1f MB", bytes / (1024.0 * 1024));
        return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
    }
    
    /**
     * 格式化持续时间
     */
    private static String formatDuration(long millis) {
        long seconds = millis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%d天 %d小时 %d分钟", days, hours % 24, minutes % 60);
        } else if (hours > 0) {
            return String.format("%d小时 %d分钟", hours, minutes % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟 %d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }
}
