package com.example.chat;

import com.example.chat.service.OpenaiClient;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
class ChatApplicationTests {
    public static void main(String[] args) {
        OpenaiClient openaiClient = new OpenaiClient();
        List<ChatInterface.OpenAIMessage> 你好 = openaiClient.generateOpenaiResponse(new ArrayList<>(), "你好");
        System.out.println();
    }
    @Test
    void contextLoads() {
        OpenaiClient openaiClient = new OpenaiClient();
        List<ChatInterface.OpenAIMessage> 你好 = openaiClient.generateOpenaiResponse(new ArrayList<>(), "你好");
        System.out.println();
    }

}
